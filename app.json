{"expo": {"name": "Bus Tracker Morocco", "slug": "bus-tracker-morocco", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/natively-dark.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/natively-dark.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs location access to track bus locations and provide real-time updates to passengers.", "NSLocationWhenInUseUsageDescription": "This app needs location access to show your current location and nearby buses.", "NSLocationAlwaysUsageDescription": "This app needs background location access to continue tracking bus locations when the app is closed.", "UIBackgroundModes": ["location", "background-fetch", "background-processing"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/natively-dark.png", "backgroundColor": "#ffffff"}, "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "FOREGROUND_SERVICE", "FOREGROUND_SERVICE_LOCATION", "WAKE_LOCK", "RECEIVE_BOOT_COMPLETED"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/natively-dark.png"}, "plugins": ["expo-router", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location to track buses and provide real-time updates.", "locationAlwaysPermission": "Allow $(PRODUCT_NAME) to use your location even when the app is closed to continue tracking bus locations.", "locationWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location to show nearby buses and your current position.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true, "isAndroidForegroundServiceEnabled": true}], ["expo-background-fetch", {"backgroundFetchHeadlessTask": "./services/backgroundService.ts"}], ["expo-task-manager"], "expo-localization", "expo-maps"], "experiments": {"typedRoutes": true}}}