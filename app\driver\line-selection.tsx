
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  TextInput,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles, shadowStyles, fontFamily } from '@/styles/commonStyles';
import { getBusLinesForCompanyAndCity } from '@/data/cities';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import i18n from '@/localization/i18n';

interface BusLine {
  id: string;
  name: string;
  route: string;
  color: string;
  isSelected: boolean;
  selectedBy?: string;
}

export default function LineSelection() {
  const router = useRouter();
  const [busLines, setBusLines] = useState<BusLine[]>([]);
  const [filteredLines, setFilteredLines] = useState<BusLine[]>([]);
  const [selectedLine, setSelectedLine] = useState<string>('');
  const [driverData, setDriverData] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDriverData();
  }, []);

  useEffect(() => {
    // Filter lines based on search query
    if (searchQuery.trim() === '') {
      setFilteredLines(busLines);
    } else {
      const filtered = busLines.filter(line =>
        line.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        line.route.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredLines(filtered);
    }
  }, [searchQuery, busLines]);

  const loadDriverData = async () => {
    try {
      const driverProfile = await AsyncStorage.getItem('driverProfile');
      if (driverProfile) {
        const driver = JSON.parse(driverProfile);
        setDriverData(driver);
        console.log('Driver data loaded:', driver);

        // Load bus lines for this driver's company and city
        const lines = getBusLinesForCompanyAndCity(driver.busCompany, driver.city);
        await loadBusLinesStatus(lines);
      } else {
        Alert.alert(i18n.t('error'), 'Driver profile not found. Please register first.');
        router.replace('/driver/register');
      }
    } catch (error) {
      console.error('Error loading driver data:', error);
      Alert.alert(i18n.t('error'), 'Failed to load driver data.');
    } finally {
      setLoading(false);
    }
  };

  const loadBusLinesStatus = async (lines: any[]) => {
    try {
      // Load selected lines status from storage
      const selectedLinesData = await AsyncStorage.getItem('selectedBusLines');
      const selectedLines = selectedLinesData ? JSON.parse(selectedLinesData) : {};

      const linesWithStatus = lines.map(line => ({
        ...line,
        isSelected: !!selectedLines[line.id],
        selectedBy: selectedLines[line.id]?.driverName || null,
      }));

      setBusLines(linesWithStatus);
      setFilteredLines(linesWithStatus);
      console.log('Bus lines loaded with status:', linesWithStatus);
    } catch (error) {
      console.error('Error loading bus lines status:', error);
      setBusLines(lines);
      setFilteredLines(lines);
    }
  };

  const handleLineSelect = async (lineId: string) => {
    const line = busLines.find(l => l.id === lineId);

    if (!line) return;

    if (line.isSelected && line.selectedBy !== driverData?.fullName) {
      Alert.alert(
        'Line Unavailable',
        `This line is currently selected by ${line.selectedBy}. Please choose another line.`
      );
      return;
    }

    console.log('Selected line:', lineId);
    setSelectedLine(lineId);
  };

  const handleStartShift = async () => {
    if (!selectedLine) {
      Alert.alert(i18n.t('error'), 'Please select a bus line first.');
      return;
    }

    if (!driverData) {
      Alert.alert(i18n.t('error'), 'Driver data not found. Please log in again.');
      return;
    }

    try {
      console.log('Starting shift for line:', selectedLine);

      // Mark line as selected
      const selectedLinesData = await AsyncStorage.getItem('selectedBusLines');
      const selectedLines = selectedLinesData ? JSON.parse(selectedLinesData) : {};

      selectedLines[selectedLine] = {
        driverName: driverData.fullName,
        driverId: driverData.id,
        startTime: new Date().toISOString(),
      };

      await AsyncStorage.setItem('selectedBusLines', JSON.stringify(selectedLines));

      // Store current shift data
      const shiftData = {
        lineId: selectedLine,
        lineName: busLines.find(l => l.id === selectedLine)?.name,
        driverId: driverData.id,
        driverName: driverData.fullName,
        startTime: new Date().toISOString(),
        isActive: true,
      };

      await AsyncStorage.setItem('currentShift', JSON.stringify(shiftData));

      console.log('Shift started successfully:', shiftData);

      // Navigate directly without alert to avoid potential issues
      router.replace('/driver/active-shift');

    } catch (error) {
      console.error('Error starting shift:', error);
      Alert.alert(i18n.t('error'), 'Failed to start shift. Please try again.');
    }
  };

  const handleBack = () => {
    console.log('Going back to login');
    router.back();
  };

  if (loading) {
    return (
      <View style={[commonStyles.centerContent, { backgroundColor: colors.background }]}>
        <IconSymbol name="bus" size={60} color={colors.primary} />
        <Text style={[commonStyles.text, { color: colors.text, marginTop: 16 }]}>
          {i18n.t('loading')}
        </Text>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: i18n.t('selectBusLine'),
          headerShown: false,
        }}
      />

      <KeyboardAvoidingView
        style={[commonStyles.container, { backgroundColor: colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Custom Header */}
        <View style={styles.customHeader}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <IconSymbol name="chevron.left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {i18n.t('selectBusLine')}
          </Text>
          {/* Profile Icon */}
          <TouchableOpacity
            style={styles.profileButton}
            onPress={() => router.push('/driver/profile')}
          >
            <IconSymbol name="person.circle.fill" size={28} color={colors.primary} />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header Section */}
          <Animated.View
            entering={FadeInUp.delay(200).duration(500)}
            style={styles.headerSection}
          >
            <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
              <IconSymbol name="list.bullet" size={40} color={colors.card} />
            </View>
            <Text style={[commonStyles.subtitle, styles.title]}>
              {i18n.t('selectBusLine')}
            </Text>
            {driverData && (
              <Text style={[commonStyles.textSecondary, styles.subtitle]}>
                Welcome back, {driverData.fullName}
              </Text>
            )}
          </Animated.View>

          {/* Search Bar */}
          <Animated.View
            entering={FadeInDown.delay(300).duration(500)}
            style={styles.searchContainer}
          >
            <View style={styles.searchInputContainer}>
              <IconSymbol name="magnifyingglass" size={20} color={colors.textSecondary} />
              <TextInput
                style={[styles.searchInput, { color: colors.text }]}
                placeholder={i18n.t('searchLines')}
                placeholderTextColor={colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <IconSymbol name="xmark.circle.fill" size={20} color={colors.textSecondary} />
                </TouchableOpacity>
              )}
            </View>
          </Animated.View>

          {/* Available Lines */}
          <Animated.View
            entering={FadeInDown.delay(400).duration(500)}
            style={styles.linesSection}
          >
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {i18n.t('availableLines')} ({filteredLines.length})
            </Text>

            {filteredLines.length === 0 ? (
              <View style={styles.noResultsContainer}>
                <IconSymbol name="magnifyingglass" size={48} color={colors.textSecondary} />
                <Text style={[styles.noResultsText, { color: colors.textSecondary }]}>
                  No bus lines found
                </Text>
                <Text style={[styles.noResultsSubtext, { color: colors.textSecondary }]}>
                  Try adjusting your search terms
                </Text>
              </View>
            ) : (
              <View style={styles.linesContainer}>
                {filteredLines.map((line, index) => (
                  <Animated.View
                    key={line.id}
                    entering={FadeInDown.delay(500 + index * 100).duration(500)}
                  >
                    <TouchableOpacity
                      style={[
                        styles.lineCard,
                        { backgroundColor: colors.card },
                        selectedLine === line.id && styles.selectedLineCard,
                        line.isSelected && line.selectedBy !== driverData?.fullName && styles.unavailableLineCard
                      ]}
                      onPress={() => handleLineSelect(line.id)}
                      disabled={line.isSelected && line.selectedBy !== driverData?.fullName}
                      activeOpacity={0.7}
                    >
                      <View style={styles.lineContent}>
                        <View style={styles.lineInfo}>
                          <View style={styles.lineHeader}>
                            <View style={[styles.lineColorIndicator, { backgroundColor: line.color }]} />
                            <Text style={[
                              styles.lineName,
                              { color: colors.text },
                              selectedLine === line.id && { color: colors.primary },
                              line.isSelected && line.selectedBy !== driverData?.fullName && { color: colors.textSecondary }
                            ]}>
                              {line.name}
                            </Text>
                          </View>
                          <Text style={[
                            styles.lineRoute,
                            { color: colors.textSecondary },
                            line.isSelected && line.selectedBy !== driverData?.fullName && { opacity: 0.6 }
                          ]}>
                            {line.route}
                          </Text>

                          {line.isSelected && line.selectedBy && (
                            <View style={styles.selectedByContainer}>
                              <IconSymbol name="person.fill" size={14} color={colors.accent} />
                              <Text style={[styles.selectedByText, { color: colors.accent }]}>
                                {i18n.t('selectedBy')} {line.selectedBy}
                              </Text>
                            </View>
                          )}
                        </View>

                        <View style={styles.lineActions}>
                          {selectedLine === line.id ? (
                            <View style={[styles.selectedIcon, { backgroundColor: colors.primary }]}>
                              <IconSymbol name="checkmark" size={20} color={colors.card} />
                            </View>
                          ) : line.isSelected && line.selectedBy !== driverData?.fullName ? (
                            <View style={[styles.unavailableIcon, { backgroundColor: colors.textSecondary }]}>
                              <IconSymbol name="lock.fill" size={16} color={colors.card} />
                            </View>
                          ) : (
                            <View style={[styles.selectIcon, { borderColor: colors.border }]}>
                              <IconSymbol name="chevron.right" size={16} color={colors.textSecondary} />
                            </View>
                          )}
                        </View>
                      </View>
                    </TouchableOpacity>
                  </Animated.View>
                ))}
              </View>
            )}
          </Animated.View>
        </ScrollView>

        {/* Start Shift Button */}
        {selectedLine && (
          <Animated.View
            entering={FadeInUp.delay(600).duration(500)}
            style={styles.startShiftContainer}
          >
            <TouchableOpacity
              style={[buttonStyles.elevated, styles.startShiftButton]}
              onPress={handleStartShift}
            >
              <Text style={[commonStyles.buttonText, styles.startShiftButtonText]}>
                {i18n.t('startShift')}
              </Text>
              <IconSymbol name="play.fill" size={20} color={colors.card} />
            </TouchableOpacity>
          </Animated.View>
        )}
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: colors.background,
    ...shadowStyles.small,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  profileButton: {
    padding: 8,
    marginRight: -8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  headerSection: {
    alignItems: 'center',
    padding: 20,
    paddingTop: 30,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    ...shadowStyles.medium,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.card,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    ...shadowStyles.small,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: fontFamily.regular,
    ...Platform.select({
      web: { outline: 'none' },
    }),
  },
  linesSection: {
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
    marginBottom: 16,
  },
  linesContainer: {
    gap: 12,
  },
  lineCard: {
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: 'transparent',
    ...shadowStyles.small,
  },
  selectedLineCard: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '08',
    ...shadowStyles.medium,
  },
  unavailableLineCard: {
    opacity: 0.6,
    backgroundColor: colors.textSecondary + '08',
  },
  lineContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lineInfo: {
    flex: 1,
  },
  lineHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  lineColorIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  lineName: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
  },
  lineRoute: {
    fontSize: 14,
    marginBottom: 8,
    marginLeft: 24,
  },
  selectedByContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginLeft: 24,
  },
  selectedByText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  lineActions: {
    marginLeft: 16,
  },
  selectedIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unavailableIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noResultsContainer: {
    alignItems: 'center',
    padding: 40,
  },
  noResultsText: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: fontFamily.medium,
    marginTop: 16,
    marginBottom: 8,
  },
  noResultsSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  startShiftContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.background,
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 30,
    ...shadowStyles.large,
  },
  startShiftButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  startShiftButtonText: {
    fontSize: 18,
  },
});
