
import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles, shadowStyles, fontFamily } from '@/styles/commonStyles';
import { cities, busCompanies } from '@/data/cities';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import i18n from '@/localization/i18n';

export default function DriverRegister() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phoneNumber: '',
    busCompany: '',
    city: '',
    licenseNumber: '',
    busNumber: '',
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    const { fullName, email, password, confirmPassword, phoneNumber, busCompany, city, licenseNumber } = formData;

    if (!fullName.trim()) {
      Alert.alert(i18n.t('error'), i18n.t('enterFullName'));
      return false;
    }

    if (!email.trim() || !email.includes('@')) {
      Alert.alert(i18n.t('error'), i18n.t('enterValidEmail'));
      return false;
    }

    if (password.length < 6) {
      Alert.alert(i18n.t('error'), i18n.t('passwordTooShort'));
      return false;
    }

    if (password !== confirmPassword) {
      Alert.alert(i18n.t('error'), i18n.t('passwordsDoNotMatch'));
      return false;
    }

    if (!phoneNumber.trim()) {
      Alert.alert(i18n.t('error'), i18n.t('enterPhoneNumber'));
      return false;
    }

    if (!busCompany) {
      Alert.alert(i18n.t('error'), i18n.t('selectBusCompany'));
      return false;
    }

    if (!city) {
      Alert.alert(i18n.t('error'), i18n.t('selectCity'));
      return false;
    }

    if (!licenseNumber.trim()) {
      Alert.alert(i18n.t('error'), i18n.t('enterLicenseNumber'));
      return false;
    }

    return true;
  };

  const handleRegister = async () => {
    console.log('Registration attempt:', { ...formData, password: '***', confirmPassword: '***' });

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Store driver data locally (in a real app, this would be sent to backend)
      const driverData = {
        ...formData,
        id: Date.now().toString(),
        registrationDate: new Date().toISOString(),
        isActive: true,
      };

      // Remove password from stored data for security
      const { password, confirmPassword, ...dataToStore } = driverData;

      await AsyncStorage.setItem('driverProfile', JSON.stringify(dataToStore));
      await AsyncStorage.setItem('isDriverRegistered', 'true');

      console.log('Driver registered successfully');

      Alert.alert(
        i18n.t('registrationSuccessful'),
        i18n.t('registrationSuccessMessage'),
        [
          {
            text: i18n.t('ok'),
            onPress: () => router.replace({
              pathname: '/driver/otp-verification',
              params: { phoneNumber: formData.phoneNumber }
            })
          }
        ]
      );
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert(i18n.t('error'), 'Failed to register. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    console.log('Going back to login');
    router.back();
  };

  // Filter cities based on selected bus company
  const availableCities = formData.busCompany
    ? cities.filter(city => city.busCompanies.includes(formData.busCompany))
    : cities;

  return (
    <>
      <Stack.Screen
        options={{
          title: i18n.t('driverRegistration'),
          headerShown: false,
        }}
      />

      <View style={[commonStyles.container, { backgroundColor: colors.background }]}>
        {/* Custom Header */}
        <View style={styles.customHeader}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <IconSymbol name="chevron.left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {i18n.t('driverRegistration')}
          </Text>
        </View>

        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            nestedScrollEnabled={true}
            scrollEventThrottle={16}
            bounces={true}
            alwaysBounceVertical={true}
          >
            {/* Header Section */}
            <Animated.View
              entering={FadeInUp.delay(200).duration(500)}
              style={styles.header}
            >
              <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
                <IconSymbol name="person.badge.plus" size={40} color={colors.card} />
              </View>
              <Text style={[commonStyles.title, { color: colors.text }]}>
                {i18n.t('driverRegistration')}
              </Text>
              <Text style={[commonStyles.textSecondary, { textAlign: 'center' }]}>
                {i18n.t('createDriverAccount')}
              </Text>
            </Animated.View>

            <Animated.View
              entering={FadeInDown.delay(400).duration(500)}
              style={styles.formContainer}
            >
              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {i18n.t('fullName')} *
                </Text>
                <TextInput
                  style={[commonStyles.input, { backgroundColor: colors.card }]}
                  placeholder={i18n.t('fullName')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.fullName}
                  onChangeText={(value) => handleInputChange('fullName', value)}
                  autoCapitalize="words"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {i18n.t('email')} *
                </Text>
                <TextInput
                  style={[commonStyles.input, { backgroundColor: colors.card }]}
                  placeholder={i18n.t('email')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.email}
                  onChangeText={(value) => handleInputChange('email', value)}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {i18n.t('phoneNumber')} *
                </Text>
                <TextInput
                  style={[commonStyles.input, { backgroundColor: colors.card }]}
                  placeholder={i18n.t('phoneNumber')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.phoneNumber}
                  onChangeText={(value) => handleInputChange('phoneNumber', value)}
                  keyboardType="phone-pad"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {i18n.t('busCompany')} *
                </Text>
                <View style={styles.pickerContainer}>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsScroll}>
                    {busCompanies.map((company) => (
                      <TouchableOpacity
                        key={company.id}
                        style={[
                          styles.optionButton,
                          {
                            backgroundColor: formData.busCompany === company.id ? colors.primary : colors.card,
                            borderColor: formData.busCompany === company.id ? colors.primary : colors.border,
                          }
                        ]}
                        onPress={() => {
                          handleInputChange('busCompany', company.id);
                          // Reset city when company changes
                          if (formData.city) {
                            handleInputChange('city', '');
                          }
                        }}
                      >
                        <Text style={[
                          styles.optionText,
                          { color: formData.busCompany === company.id ? colors.card : colors.text }
                        ]}>
                          {company.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {i18n.t('city')} *
                </Text>
                <View style={styles.pickerContainer}>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsScroll}>
                    {availableCities.map((city) => (
                      <TouchableOpacity
                        key={city.id}
                        style={[
                          styles.optionButton,
                          {
                            backgroundColor: formData.city === city.id ? colors.primary : colors.card,
                            borderColor: formData.city === city.id ? colors.primary : colors.border,
                          }
                        ]}
                        onPress={() => handleInputChange('city', city.id)}
                      >
                        <Text style={[
                          styles.optionText,
                          { color: formData.city === city.id ? colors.card : colors.text }
                        ]}>
                          {city.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
                {!formData.busCompany && (
                  <Text style={[styles.helperText, { color: colors.textSecondary }]}>
                    Please select a bus company first
                  </Text>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {i18n.t('driverLicenseNumber')} *
                </Text>
                <TextInput
                  style={[commonStyles.input, { backgroundColor: colors.card }]}
                  placeholder={i18n.t('driverLicenseNumber')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.licenseNumber}
                  onChangeText={(value) => handleInputChange('licenseNumber', value)}
                  autoCapitalize="characters"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {i18n.t('busNumber')}
                </Text>
                <TextInput
                  style={[commonStyles.input, { backgroundColor: colors.card }]}
                  placeholder={i18n.t('busNumber')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.busNumber}
                  onChangeText={(value) => handleInputChange('busNumber', value)}
                  autoCorrect={false}
                />
              </View>

              {/* Password fields with extra bottom padding for keyboard */}
              <View style={[styles.inputContainer, styles.passwordContainer]}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {i18n.t('password')} *
                </Text>
                <TextInput
                  style={[commonStyles.input, { backgroundColor: colors.card }]}
                  placeholder={i18n.t('password')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.password}
                  onChangeText={(value) => handleInputChange('password', value)}
                  secureTextEntry
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              <View style={[styles.inputContainer, styles.passwordContainer]}>
                <Text style={[styles.label, { color: colors.text }]}>
                  {i18n.t('confirmPassword')} *
                </Text>
                <TextInput
                  style={[commonStyles.input, { backgroundColor: colors.card }]}
                  placeholder={i18n.t('confirmPassword')}
                  placeholderTextColor={colors.textSecondary}
                  value={formData.confirmPassword}
                  onChangeText={(value) => handleInputChange('confirmPassword', value)}
                  secureTextEntry
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              <Animated.View
                entering={FadeInUp.delay(600).duration(500)}
              >
                <TouchableOpacity
                  style={[buttonStyles.primary, { marginTop: 32, opacity: loading ? 0.7 : 1 }]}
                  onPress={handleRegister}
                  disabled={loading}
                  activeOpacity={0.8}
                >
                  <Text style={[commonStyles.buttonText, { color: colors.card }]}>
                    {loading ? i18n.t('creatingAccount') : i18n.t('createAccount')}
                  </Text>
                </TouchableOpacity>
              </Animated.View>
            </Animated.View>

            <View style={styles.infoSection}>
              <Text style={[commonStyles.textSecondary, { textAlign: 'center', fontSize: 12 }]}>
                By creating an account, you agree to our terms of service and privacy policy
              </Text>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: colors.background,
    ...shadowStyles.small,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 150 : 180, // Extra padding for keyboard
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    ...shadowStyles.medium,
  },
  formContainer: {
    marginBottom: 30,
  },
  inputContainer: {
    marginBottom: 20,
  },
  passwordContainer: {
    marginBottom: 24, // Extra margin for password fields
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
    marginBottom: 8,
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  pickerContainer: {
    marginTop: 8,
  },
  optionsScroll: {
    flexDirection: 'row',
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    minWidth: 80,
    alignItems: 'center',
    ...shadowStyles.small,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: fontFamily.medium,
    textAlign: 'center',
  },
  infoSection: {
    marginTop: 'auto',
    paddingTop: 20,
    paddingBottom: 40, // Extra bottom padding
  },
});
