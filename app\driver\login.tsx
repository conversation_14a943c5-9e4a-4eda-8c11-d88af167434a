
import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ScrollView } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles } from '@/styles/commonStyles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from '@/localization/i18n';

export default function DriverLogin() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [checkingLogin, setCheckingLogin] = useState(true);

  // Check if driver is already logged in
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        const isLoggedIn = await AsyncStorage.getItem('driverLoggedIn');
        if (isLoggedIn === 'true') {
          console.log('Driver already logged in, redirecting to line selection');
          router.replace('/driver/line-selection');
          return;
        }
      } catch (error) {
        console.error('Error checking login status:', error);
      } finally {
        setCheckingLogin(false);
      }
    };

    checkLoginStatus();
  }, []);

  const handleLogin = async () => {
    console.log('Login attempt:', { email, password: '***' });

    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);

    try {
      // Check if driver is registered
      const isRegistered = await AsyncStorage.getItem('isDriverRegistered');
      if (!isRegistered) {
        Alert.alert(
          'Account Not Found',
          'Please register first to create your driver account.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Register', onPress: () => router.push('/driver/register') }
          ]
        );
        setLoading(false);
        return;
      }

      // Simulate login process
      setTimeout(async () => {
        setLoading(false);

        // Store login session
        await AsyncStorage.setItem('driverLoggedIn', 'true');
        await AsyncStorage.setItem('driverEmail', email);

        console.log('Login successful, navigating to line selection');
        router.replace('/driver/line-selection');
      }, 1500);
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Error', 'Login failed. Please try again.');
      setLoading(false);
    }
  };

  const handleRegister = () => {
    console.log('Navigating to registration');
    router.push('/driver/register');
  };

  const handleBack = () => {
    console.log('Going back to home');
    router.back();
  };

  // Show loading screen while checking login status
  if (checkingLogin) {
    return (
      <>
        <Stack.Screen
          options={{
            title: 'Driver Login',
            headerStyle: {
              backgroundColor: colors.primary,
            },
            headerTintColor: colors.card,
          }}
        />
        <View style={[commonStyles.container, { backgroundColor: colors.background, justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={[commonStyles.text, { color: colors.text }]}>Checking login status...</Text>
        </View>
      </>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Driver Login',
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.card,
          headerLeft: () => (
            <TouchableOpacity onPress={handleBack} style={styles.backButton}>
              <IconSymbol name="chevron.left" size={24} color={colors.card} />
            </TouchableOpacity>
          ),
        }}
      />
      <View style={[commonStyles.container, { backgroundColor: colors.background }]}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
              <IconSymbol name="car.fill" size={40} color={colors.card} />
            </View>
            <Text style={[commonStyles.title, { color: colors.text }]}>
              {i18n.t('driverLogin')}
            </Text>
            <Text style={[commonStyles.textSecondary, { textAlign: 'center' }]}>
              {i18n.t('loginToAccount')}
            </Text>
          </View>

          <View style={styles.formContainer}>
            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: colors.text }]}>{i18n.t('email')}</Text>
              <TextInput
                style={[commonStyles.input, { backgroundColor: colors.card }]}
                placeholder={i18n.t('email')}
                placeholderTextColor={colors.textSecondary}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, { color: colors.text }]}>{i18n.t('password')}</Text>
              <TextInput
                style={[commonStyles.input, { backgroundColor: colors.card }]}
                placeholder={i18n.t('password')}
                placeholderTextColor={colors.textSecondary}
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <TouchableOpacity
              style={[buttonStyles.primary, { marginTop: 20, opacity: loading ? 0.7 : 1 }]}
              onPress={handleLogin}
              disabled={loading}
            >
              <Text style={[commonStyles.buttonText, { color: colors.card }]}>
                {loading ? i18n.t('loggingIn') : i18n.t('login')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.forgotPassword}>
              <Text style={[commonStyles.textSecondary, { textAlign: 'center' }]}>
                {i18n.t('forgotPassword')}
              </Text>
            </TouchableOpacity>

            <View style={styles.divider}>
              <View style={[styles.dividerLine, { backgroundColor: colors.border }]} />
              <Text style={[styles.dividerText, { color: colors.textSecondary }]}>or</Text>
              <View style={[styles.dividerLine, { backgroundColor: colors.border }]} />
            </View>

            <TouchableOpacity
              style={[buttonStyles.outline, { borderColor: colors.primary }]}
              onPress={handleRegister}
            >
              <Text style={[commonStyles.buttonTextOutline, { color: colors.primary }]}>
                {i18n.t('createAccount')}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.infoSection}>
            <Text style={[commonStyles.textSecondary, { textAlign: 'center', fontSize: 12 }]}>
              Contact your supervisor if you need help with your account
            </Text>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 20,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  formContainer: {
    marginBottom: 40,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  forgotPassword: {
    marginTop: 20,
    padding: 10,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  dividerLine: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
  },
  infoSection: {
    marginTop: 'auto',
    paddingTop: 20,
  },
});
