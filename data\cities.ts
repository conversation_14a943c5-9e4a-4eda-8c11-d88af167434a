
export interface City {
  id: string;
  name: string;
  arabicName: string;
  region: string;
  busCompanies: string[];
}

export interface BusCompany {
  id: string;
  name: string;
  cities: string[];
}

export const cities: City[] = [
  {
    id: 'casablanca',
    name: 'Casablanca',
    arabicName: 'الدار البيضاء',
    region: 'Casablanca-Settat',
    busCompanies: ['casabus']
  },
  {
    id: 'fes',
    name: '<PERSON><PERSON>',
    arabicName: 'فاس',
    region: 'Fès-Meknès',
    busCompanies: ['citybus']
  },
  {
    id: 'marrakech',
    name: 'Marrakech',
    arabicName: 'مراكش',
    region: 'Marrakech-Safi',
    busCompanies: ['alsa']
  },
  {
    id: 'tangier',
    name: 'Tangier',
    arabicName: 'طنجة',
    region: 'Tanger-Tétouan-Al Hoceïma',
    busCompanies: ['alsa']
  },
  {
    id: 'rabat',
    name: '<PERSON><PERSON>',
    arabicName: 'الرباط',
    region: 'Rabat-Salé-Kénitra',
    busCompanies: ['alsa']
  },
  {
    id: 'meknes',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    arabicName: 'مكناس',
    region: 'Fès-Meknès',
    busCompanies: ['citybus']
  },
  {
    id: 'oujda',
    name: 'Oujda',
    arabicName: 'وجدة',
    region: 'Oriental',
    busCompanies: ['citybus']
  },
  {
    id: 'kenitra',
    name: 'Kenitra',
    arabicName: 'القنيطرة',
    region: 'Rabat-Salé-Kénitra',
    busCompanies: ['foughalbus']
  },
  {
    id: 'agadir',
    name: 'Agadir',
    arabicName: 'أكادير',
    region: 'Souss-Massa',
    busCompanies: ['alsa']
  },
  {
    id: 'inezgane',
    name: 'Inezgane',
    arabicName: 'إنزكان',
    region: 'Souss-Massa',
    busCompanies: ['alsa']
  },
  {
    id: 'tetouan',
    name: 'Tétouan',
    arabicName: 'تطوان',
    region: 'Tanger-Tétouan-Al Hoceïma',
    busCompanies: ['transdev']
  },
  {
    id: 'temara',
    name: 'Temara',
    arabicName: 'تمارة',
    region: 'Rabat-Salé-Kénitra',
    busCompanies: ['alsa']
  },
  {
    id: 'sale',
    name: 'Salé',
    arabicName: 'سلا',
    region: 'Rabat-Salé-Kénitra',
    busCompanies: ['alsa']
  },
  {
    id: 'safi',
    name: 'Safi',
    arabicName: 'آسفي',
    region: 'Marrakech-Safi',
    busCompanies: ['vectalia']
  },
  {
    id: 'khenifra',
    name: 'Khénifra',
    arabicName: 'خنيفرة',
    region: 'Béni Mellal-Khénifra',
    busCompanies: ['karamabus']
  },
  {
    id: 'eljadida',
    name: 'El Jadida',
    arabicName: 'الجديدة',
    region: 'Casablanca-Settat',
    busCompanies: ['equinox']
  },
  {
    id: 'mohammedia',
    name: 'Mohammedia',
    arabicName: 'المحمدية',
    region: 'Casablanca-Settat',
    busCompanies: ['casabus']
  },
  {
    id: 'kouribga',
    name: 'Kouribga',
    arabicName: 'خريبكة',
    region: 'Béni Mellal-Khénifra',
    busCompanies: ['alsa']
  },
  {
    id: 'fquihbensalah',
    name: 'Fquih Ben Salah',
    arabicName: 'الفقيه بن صالح',
    region: 'Béni Mellal-Khénifra',
    busCompanies: ['alsa']
  },
  {
    id: 'benimellal',
    name: 'Béni Mellal',
    arabicName: 'بني ملال',
    region: 'Béni Mellal-Khénifra',
    busCompanies: ['karamabus']
  },
  {
    id: 'taroudant',
    name: 'Taroudant',
    arabicName: 'تارودانت',
    region: 'Souss-Massa',
    busCompanies: ['karamabus']
  },
  {
    id: 'aitmelloul',
    name: 'Ait Melloul',
    arabicName: 'أيت ملول',
    region: 'Souss-Massa',
    busCompanies: ['alsa']
  },
  {
    id: 'nador',
    name: 'Nador',
    arabicName: 'الناظور',
    region: 'Oriental',
    busCompanies: ['vectalia']
  },
  {
    id: 'taza',
    name: 'Taza',
    arabicName: 'تازة',
    region: 'Fès-Meknès',
    busCompanies: ['foughalbus']
  },
  {
    id: 'settat',
    name: 'Settat',
    arabicName: 'سطات',
    region: 'Casablanca-Settat',
    busCompanies: ['luxtransport']
  },
  {
    id: 'boufekrane',
    name: 'Boufekrane',
    arabicName: 'بوفكران',
    region: 'Fès-Meknès',
    busCompanies: ['citybus']
  },
  {
    id: 'berkane',
    name: 'Berkane',
    arabicName: 'بركان',
    region: 'Oriental',
    busCompanies: ['berkanemobility']
  },
  {
    id: 'bouskoura',
    name: 'Bouskoura',
    arabicName: 'بوسكورة',
    region: 'Casablanca-Settat',
    busCompanies: ['casabus']
  },
  {
    id: 'elhajeb',
    name: 'El Hajeb',
    arabicName: 'الحاجب',
    region: 'Fès-Meknès',
    busCompanies: ['citybus']
  },
  {
    id: 'mrirt',
    name: 'Mrirt',
    arabicName: 'مريرت',
    region: 'Fès-Meknès',
    busCompanies: ['karamabus']
  }
];

export const busCompanies: BusCompany[] = [
  {
    id: 'alsa',
    name: 'Alsa',
    cities: ['tangier', 'marrakech', 'agadir', 'kouribga', 'aitmelloul', 'fquihbensalah', 'temara', 'rabat', 'inezgane', 'sale']
  },
  {
    id: 'casabus',
    name: 'Casabus',
    cities: ['casablanca', 'bouskoura', 'mohammedia']
  },
  {
    id: 'citybus',
    name: 'City Bus',
    cities: ['meknes', 'fes', 'oujda', 'elhajeb', 'boufekrane']
  },
  {
    id: 'foughalbus',
    name: 'Foughal Bus',
    cities: ['kenitra', 'taza']
  },
  {
    id: 'karamabus',
    name: 'Karama Bus',
    cities: ['khenifra', 'benimellal', 'taroudant', 'mrirt']
  },
  {
    id: 'vectalia',
    name: 'Vectalia',
    cities: ['safi', 'nador']
  },
  {
    id: 'berkanemobility',
    name: 'Berkane Mobility',
    cities: ['berkane']
  },
  {
    id: 'equinox',
    name: 'Equinox',
    cities: ['eljadida']
  },
  {
    id: 'transdev',
    name: 'Trans Dev',
    cities: ['tetouan']
  },
  {
    id: 'luxtransport',
    name: 'Lux Transport',
    cities: ['settat']
  }
];

export const getBusLinesForCompanyAndCity = (companyId: string, cityId: string) => {
  // Mock bus lines data - in a real app this would come from a database
  const busLines = [
    { id: 'L1', name: 'L1', route: 'City Center - Airport', color: '#FF6B6B' },
    { id: 'L2', name: 'L2', route: 'University - Mall', color: '#4ECDC4' },
    { id: 'L3', name: 'L3', route: 'Train Station - Beach', color: '#45B7D1' },
    { id: 'L4', name: 'L4', route: 'Hospital - Market', color: '#96CEB4' },
    { id: 'L5', name: 'L5', route: 'Industrial Zone - Downtown', color: '#FFEAA7' },
    { id: 'L6', name: 'L6', route: 'Residential - Commercial', color: '#DDA0DD' },
    { id: 'L7', name: 'L7', route: 'Port - City Center', color: '#98D8C8' },
    { id: 'L8', name: 'L8', route: 'Stadium - University', color: '#F7DC6F' },
    { id: 'L9', name: 'L9', route: 'Airport - Hotel District', color: '#BB8FCE' },
    { id: 'L10', name: 'L10', route: 'Suburbs - Downtown', color: '#85C1E9' },
    { id: 'L11', name: 'L11', route: 'Business District - Residential', color: '#F8C471' },
    { id: 'L12', name: 'L12', route: 'Medical Center - Shopping Center', color: '#82E0AA' },
    { id: 'L13', name: 'L13', route: 'Cultural Center - Sports Complex', color: '#F1948A' },
    { id: 'L14', name: 'L14', route: 'Tech Park - Old Town', color: '#85C1E9' },
    { id: 'L15', name: 'L15', route: 'Marina - Mountain View', color: '#D7BDE2' },
    { id: 'L16', name: 'L16', route: 'Government District - Suburbs', color: '#A9DFBF' },
    { id: 'L17', name: 'L17', route: 'Exhibition Center - Railway', color: '#F9E79F' },
    { id: 'L18', name: 'L18', route: 'Financial District - Residential', color: '#AED6F1' },
    { id: 'L19', name: 'L19', route: 'Entertainment District - Suburbs', color: '#E8DAEF' },
    { id: 'L20', name: 'L20', route: 'Industrial Park - City Center', color: '#ABEBC6' },
    { id: 'L21', name: 'L21', route: 'Tourist Zone - Local Markets', color: '#FAD7A0' },
    { id: 'L22', name: 'L22', route: 'Education Hub - Commercial Zone', color: '#D5A6BD' },
  ];
  
  // Return a subset based on company and city (mock logic)
  const company = busCompanies.find(c => c.id === companyId);
  if (!company || !company.cities.includes(cityId)) {
    return [];
  }
  
  // Return different lines based on city size (mock logic)
  const city = cities.find(c => c.id === cityId);
  if (!city) return [];
  
  // Major cities get more lines
  const majorCities = ['casablanca', 'rabat', 'marrakech', 'fes', 'tangier'];
  const lineCount = majorCities.includes(cityId) ? 12 : 8;
  
  return busLines.slice(0, lineCount).map(line => ({
    ...line,
    isSelected: false,
    selectedBy: null
  }));
};
