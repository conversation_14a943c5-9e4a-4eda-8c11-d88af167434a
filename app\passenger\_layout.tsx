
import React from 'react';
import { Stack } from 'expo-router';
import { Platform } from 'react-native';
import { colors } from '@/styles/commonStyles';

export default function PassengerLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        animation: Platform.select({
          ios: 'slide_from_right',
          android: 'slide_from_right',
          web: 'slide_from_right',
        }),
        animationDuration: 250,
        gestureEnabled: true,
        gestureDirection: 'horizontal',
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.card,
        headerTitleStyle: {
          fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
          fontSize: 18,
        },
        contentStyle: {
          backgroundColor: colors.background,
        },
      }}
    >
      <Stack.Screen 
        name="city-selection" 
        options={{ 
          title: 'Select City',
          headerBackVisible: true,
        }} 
      />
      <Stack.Screen 
        name="line-input" 
        options={{ 
          title: 'Select Bus Line',
          headerBackVisible: true,
        }} 
      />
      <Stack.Screen 
        name="tracking-map" 
        options={{ 
          title: 'Live Tracking',
          headerBackVisible: true,
        }} 
      />
    </Stack>
  );
}
