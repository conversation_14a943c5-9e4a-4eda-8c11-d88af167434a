
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles, shadowStyles, fontFamily } from '@/styles/commonStyles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import i18n from '@/localization/i18n';

export default function OTPVerification() {
  const router = useRouter();
  const { phoneNumber } = useLocalSearchParams();
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  
  const inputRefs = useRef<TextInput[]>([]);

  useEffect(() => {
    // Start countdown timer
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleOtpChange = (value: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOtp = async () => {
    const otpCode = otp.join('');
    
    if (otpCode.length !== 6) {
      Alert.alert(i18n.t('error'), 'Please enter the complete 6-digit code');
      return;
    }

    console.log('Verifying OTP:', otpCode);
    setLoading(true);

    try {
      // Mock OTP verification (in real app, this would call backend)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // For demo purposes, accept any 6-digit code
      if (otpCode.length === 6) {
        await AsyncStorage.setItem('isDriverVerified', 'true');
        
        Alert.alert(
          i18n.t('success'),
          'Phone number verified successfully!',
          [
            {
              text: i18n.t('ok'),
              onPress: () => router.replace('/driver/line-selection')
            }
          ]
        );
      } else {
        Alert.alert(i18n.t('error'), 'Invalid verification code. Please try again.');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      Alert.alert(i18n.t('error'), 'Verification failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!canResend) return;

    console.log('Resending OTP to:', phoneNumber);
    setCanResend(false);
    setResendTimer(60);
    setOtp(['', '', '', '', '', '']);

    // Reset timer
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Mock resend (in real app, this would call backend)
    Alert.alert(i18n.t('success'), 'Verification code sent successfully!');
  };

  const handleBack = () => {
    console.log('Going back to registration');
    router.back();
  };

  const isOtpComplete = otp.every(digit => digit !== '');

  return (
    <>
      <Stack.Screen
        options={{
          title: i18n.t('otpVerification'),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.card,
          headerLeft: () => (
            <TouchableOpacity onPress={handleBack} style={styles.backButton}>
              <IconSymbol name="chevron.left" size={24} color={colors.card} />
            </TouchableOpacity>
          ),
        }}
      />
      
      <KeyboardAvoidingView 
        style={[commonStyles.container, { backgroundColor: colors.background }]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header Section */}
          <Animated.View 
            entering={FadeInUp.delay(200).duration(500)}
            style={styles.headerSection}
          >
            <View style={[styles.iconContainer, { backgroundColor: colors.primary }]}>
              <IconSymbol name="message.fill" size={40} color={colors.card} />
            </View>
            <Text style={[commonStyles.title, { color: colors.text }]}>
              {i18n.t('otpVerification')}
            </Text>
            <Text style={[commonStyles.textSecondary, styles.subtitle]}>
              {i18n.t('otpSent')}
            </Text>
            <Text style={[styles.phoneNumber, { color: colors.primary }]}>
              {phoneNumber}
            </Text>
          </Animated.View>

          {/* OTP Input Section */}
          <Animated.View 
            entering={FadeInDown.delay(400).duration(500)}
            style={styles.otpContainer}
          >
            <Text style={[styles.otpLabel, { color: colors.text }]}>
              {i18n.t('enterOtp')}
            </Text>
            
            <View style={styles.otpInputContainer}>
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={(ref) => {
                    if (ref) inputRefs.current[index] = ref;
                  }}
                  style={[
                    styles.otpInput,
                    { 
                      backgroundColor: colors.card,
                      borderColor: digit ? colors.primary : colors.border,
                      color: colors.text
                    }
                  ]}
                  value={digit}
                  onChangeText={(value) => handleOtpChange(value, index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  keyboardType="numeric"
                  maxLength={1}
                  textAlign="center"
                  selectTextOnFocus
                />
              ))}
            </View>
          </Animated.View>

          {/* Verify Button */}
          <Animated.View 
            entering={FadeInDown.delay(600).duration(500)}
            style={styles.verifyContainer}
          >
            <TouchableOpacity
              style={[
                buttonStyles.primary,
                { 
                  opacity: isOtpComplete && !loading ? 1 : 0.6,
                  marginBottom: 16
                }
              ]}
              onPress={handleVerifyOtp}
              disabled={!isOtpComplete || loading}
            >
              <Text style={[commonStyles.buttonText, { color: colors.card }]}>
                {loading ? 'Verifying...' : i18n.t('verifyCode')}
              </Text>
            </TouchableOpacity>
          </Animated.View>

          {/* Resend Section */}
          <Animated.View 
            entering={FadeInDown.delay(800).duration(500)}
            style={styles.resendContainer}
          >
            <Text style={[styles.resendText, { color: colors.textSecondary }]}>
              Didn't receive the code?
            </Text>
            
            {canResend ? (
              <TouchableOpacity onPress={handleResendCode}>
                <Text style={[styles.resendButton, { color: colors.primary }]}>
                  {i18n.t('resendCode')}
                </Text>
              </TouchableOpacity>
            ) : (
              <Text style={[styles.timerText, { color: colors.textSecondary }]}>
                Resend in {resendTimer}s
              </Text>
            )}
          </Animated.View>

          {/* Info Section */}
          <Animated.View 
            entering={FadeInUp.delay(1000).duration(500)}
            style={styles.infoSection}
          >
            <View style={[styles.infoCard, { backgroundColor: colors.primary + '08' }]}>
              <IconSymbol name="info.circle.fill" size={20} color={colors.primary} />
              <Text style={[styles.infoText, { color: colors.text }]}>
                Enter the 6-digit verification code sent to your phone number. 
                This helps us verify your identity and secure your account.
              </Text>
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 48,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 20,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    ...shadowStyles.medium,
  },
  subtitle: {
    textAlign: 'center',
    paddingHorizontal: 20,
    marginBottom: 8,
    fontSize: 16,
  },
  phoneNumber: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
  },
  otpContainer: {
    marginBottom: 40,
  },
  otpLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
    textAlign: 'center',
    marginBottom: 24,
  },
  otpInputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  otpInput: {
    width: 48,
    height: 56,
    borderRadius: 12,
    borderWidth: 2,
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: fontFamily.bold,
    ...shadowStyles.small,
  },
  verifyContainer: {
    marginBottom: 32,
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  resendText: {
    fontSize: 14,
    marginBottom: 8,
  },
  resendButton: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: fontFamily.medium,
  },
  timerText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  infoSection: {
    marginTop: 'auto',
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderRadius: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: colors.primary + '20',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
});
