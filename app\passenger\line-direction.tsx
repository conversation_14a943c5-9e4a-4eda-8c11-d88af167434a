import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles } from '@/styles/commonStyles';

import LoadingSpinner from '@/components/LoadingSpinner';
import i18n from '@/localization/i18n';

export default function LineDirection() {
  const router = useRouter();
  const { city, cityName, lineNumber } = useLocalSearchParams();
  const [selectedDirection, setSelectedDirection] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Mock directions for the selected line
  const directions = [
    { id: 'A', name: 'Direction A', description: 'City Center → Airport' },
    { id: 'B', name: 'Direction B', description: 'Airport → City Center' },
  ];

  const handleDirectionSelect = (direction: string) => {
    setSelectedDirection(direction);
  };

  const handleTrackBus = async () => {
    if (!selectedDirection) {
      Alert.alert('Error', 'Please select a direction');
      return;
    }

    setIsLoading(true);

    // Simulate loading time for better UX
    setTimeout(() => {
      const fullLineNumber = `${lineNumber}-${selectedDirection}`;
      console.log('Tracking bus:', fullLineNumber, 'in city:', cityName);
      router.replace(`/passenger/tracking-map?city=${city}&cityName=${cityName}&line=${fullLineNumber}`);
      setIsLoading(false);
    }, 1500);
  };

  const handleBack = () => {
    console.log('Going back to line input');
    router.back();
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: i18n.t('selectDirection'),
          headerStyle: {
            backgroundColor: colors.passenger,
          },
          headerTintColor: colors.card,
          headerLeft: () => (
            <TouchableOpacity onPress={handleBack} style={styles.backButton}>
              <IconSymbol name="chevron.left" size={24} color={colors.card} />
            </TouchableOpacity>
          ),
        }}
      />
      <View style={[commonStyles.container, { backgroundColor: colors.background }]}>


        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={[commonStyles.title, { color: colors.text }]}>
              {i18n.t('selectDirection')}
            </Text>
            <Text style={[commonStyles.textSecondary, { textAlign: 'center' }]}>
              for Line {lineNumber} in {cityName}
            </Text>
          </View>

          <View style={styles.directionsSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {i18n.t('chooseDirection')}
            </Text>

            {directions.map((direction) => (
              <TouchableOpacity
                key={direction.id}
                style={[
                  styles.directionCard,
                  {
                    backgroundColor: colors.card,
                    borderColor: selectedDirection === direction.id ? colors.passenger : colors.border,
                    borderWidth: selectedDirection === direction.id ? 2 : 1,
                  }
                ]}
                onPress={() => handleDirectionSelect(direction.id)}
                activeOpacity={0.7}
              >
                <View style={styles.directionContent}>
                  <View style={styles.directionHeader}>
                    <Text style={[styles.directionName, { color: colors.text }]}>
                      {direction.name}
                    </Text>
                    <View style={[
                      styles.radioButton,
                      {
                        borderColor: selectedDirection === direction.id ? colors.passenger : colors.border,
                        backgroundColor: selectedDirection === direction.id ? colors.passenger : 'transparent',
                      }
                    ]}>
                      {selectedDirection === direction.id && (
                        <IconSymbol name="checkmark" size={16} color={colors.card} />
                      )}
                    </View>
                  </View>
                  <Text style={[styles.directionDescription, { color: colors.textSecondary }]}>
                    {direction.description}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.actionContainer}>
            <TouchableOpacity
              style={[
                buttonStyles.passenger,
                {
                  opacity: selectedDirection ? 1 : 0.5,
                  backgroundColor: selectedDirection ? colors.passenger : colors.textSecondary
                }
              ]}
              onPress={handleTrackBus}
              disabled={!selectedDirection || isLoading}
            >
              <Text style={[commonStyles.buttonText, { color: colors.card }]}>
                {i18n.t('trackBus')}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Loading Overlay */}
        {isLoading && (
          <View style={styles.loadingOverlay}>
            <LoadingSpinner
              message="Connecting to bus tracking system..."
              size="large"
              color={colors.passenger}
            />
          </View>
        )}


      </View>
    </>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  directionsSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  directionCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  directionContent: {
    flex: 1,
  },
  directionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  directionName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  directionDescription: {
    fontSize: 14,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionContainer: {
    marginTop: 'auto',
    paddingTop: 20,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
});
