
import React from 'react';
import { Stack } from 'expo-router';
import { Platform } from 'react-native';
import { colors } from '@/styles/commonStyles';

export default function DriverLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        animation: Platform.select({
          ios: 'slide_from_right',
          android: 'slide_from_right',
          web: 'slide_from_right',
        }),
        animationDuration: 250,
        gestureEnabled: true,
        gestureDirection: 'horizontal',
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.card,
        headerTitleStyle: {
          fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
          fontSize: 18,
        },
        contentStyle: {
          backgroundColor: colors.background,
        },
      }}
    >
      <Stack.Screen 
        name="login" 
        options={{ 
          title: 'Driver Login',
          headerBackVisible: true,
        }} 
      />
      <Stack.Screen 
        name="register" 
        options={{ 
          title: 'Driver Registration',
          headerBackVisible: true,
        }} 
      />
      <Stack.Screen 
        name="line-selection" 
        options={{ 
          title: 'Select Line',
          headerBackVisible: true,
        }} 
      />
      <Stack.Screen 
        name="active-shift" 
        options={{ 
          title: 'Active Shift',
          headerBackVisible: false,
        }} 
      />
    </Stack>
  );
}
