
import { I18n } from 'i18n-js';
import * as Localization from 'expo-localization';

const i18n = new I18n({
  en: {
    // Common
    back: 'Back',
    continue: 'Continue',
    cancel: 'Cancel',
    ok: 'OK',
    error: 'Error',
    success: 'Success',
    loading: 'Loading...',
    refresh: 'Refresh',
    search: 'Search',
    retry: 'Retry',
    openSettings: 'Open Settings',

    // Location Errors
    locationError: 'Location Error',
    locationServicesDisabled: 'Location Services Disabled',
    locationPermissionDenied: 'Location Permission Denied',
    enableLocationServices: 'Please enable location services in your device settings.',
    grantLocationPermission: 'Please grant location access in your device settings.',
    
    // Home Screen
    appTitle: 'Bus Tracker Morocco',
    appSubtitle: 'Real-time bus tracking for Morocco\'s public transportation',
    imADriver: 'I\'m a Driver',
    imAPassenger: 'I\'m a Passenger',
    driverDescription: 'Start your shift and begin tracking your bus route',
    passengerDescription: 'Track buses in real-time and plan your journey',
    selectLine: 'Select your line',
    trackRoute: 'Track your route',
    manageShifts: 'Manage shifts',
    findNearbyBuses: 'Find nearby buses',
    realTimeTracking: 'Real-time tracking',
    etaEstimates: 'ETA estimates',
    
    // Driver Registration
    driverRegistration: 'Driver Registration',
    createDriverAccount: 'Create your driver account to start working',
    fullName: 'Full Name',
    email: 'Email',
    phoneNumber: 'Phone Number',
    busCompany: 'Bus Company',
    city: 'City',
    driverLicenseNumber: 'Driver License Number',
    busNumber: 'Bus Number (Optional)',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    createAccount: 'Create Account',
    creatingAccount: 'Creating Account...',
    registrationSuccessful: 'Registration Successful',
    registrationSuccessMessage: 'Your driver account has been created successfully. You can now log in.',
    
    // OTP Verification
    otpVerification: 'OTP Verification',
    otpSent: 'We\'ve sent a verification code to your phone number',
    enterOtp: 'Enter verification code',
    verifyCode: 'Verify Code',
    resendCode: 'Resend Code',

    // Driver Login
    driverLogin: 'Driver Login',
    welcomeBack: 'Welcome Back!',
    loginToAccount: 'Login to your driver account',
    login: 'Login',
    loggingIn: 'Logging in...',
    forgotPassword: 'Forgot Password?',
    dontHaveAccount: 'Don\'t have an account?',
    registerHere: 'Register here',

    // Active Shift
    activeShift: 'Active Shift',
    shiftDuration: 'Shift Duration',
    currentLine: 'Current Line',
    endShift: 'End Shift',
    pauseTracking: 'Pause Tracking',
    resumeTracking: 'Resume Tracking',
    shiftEnded: 'Shift Ended',
    shiftEndedMessage: 'Your shift has been ended successfully.',
    confirmEndShift: 'Are you sure you want to end your shift?',
    
    // City Selection
    selectYourCity: 'Select Your City',
    chooseYourCity: 'Choose Your City',
    selectCityDescription: 'Select the city where you want to track buses',
    searchCities: 'Search cities...',
    continueToLines: 'Continue to Bus Lines',
    noResultsFound: 'No cities found matching',
    tryDifferentSearch: 'Try searching with a different term',
    
    // Line Selection
    selectBusLine: 'Select Bus Line',
    searchLines: 'Search bus lines...',
    availableLines: 'Available Lines',
    selectedBy: 'Selected by',
    startShift: 'Start Shift',
    chooseLineForShift: 'Choose your bus line for this shift',
    startingShift: 'Starting shift...',
    enterLineNumber: 'Enter Line Number',
    trackBus: 'Track Bus',

    // Direction Selection
    selectDirection: 'Select Direction',
    chooseDirection: 'Choose Direction',
    continueToDirection: 'Continue to Direction',
    directionA: 'Direction A',
    directionB: 'Direction B',

    // Station Information
    routeStations: 'Route Stations',
    passedStations: 'Passed Stations',
    currentLocation: 'Current Location',
    upcomingStations: 'Upcoming Stations',
    enRoute: 'En route',
    
    // Tracking Map
    interactiveMap: 'Interactive Map',
    mapNotSupported: 'Maps are fully supported in this app',
    tapToToggle: 'Tap to hide/show bus line info',
    youAreHere: 'You are here',
    locationAccessDenied: 'Location access denied',
    enable: 'Enable',
    lineVarieties: 'Line Varieties',
    busesInCity: 'buses in',
    nextStop: 'Next',
    eta: 'ETA',
    details: 'Details',
    arrivesIn: 'Arrives in',
    occupancy: 'Occupancy',
    updatingLocations: 'Updating locations...',
    
    // Profile
    profile: 'Profile',
    editProfile: 'Edit Profile',
    driverInfo: 'Driver Information',
    
    // Onboarding
    welcome: 'Welcome to Bus Tracker Morocco',
    chooseUserType: 'Choose your user type to get started',
    driverSteps: 'Driver Steps',
    passengerSteps: 'Passenger Steps',
    howToUseAsDriver: 'How to use as Driver',
    howToUseAsPassenger: 'How to use as Passenger',
    
    // Validation Messages
    enterFullName: 'Please enter your full name',
    enterValidEmail: 'Please enter a valid email address',
    passwordTooShort: 'Password must be at least 6 characters long',
    passwordsDoNotMatch: 'Passwords do not match',
    enterPhoneNumber: 'Please enter your phone number',
    selectBusCompany: 'Please select your bus company',
    selectCity: 'Please select your city',
    enterLicenseNumber: 'Please enter your license number',
    noCitySelected: 'No city selected',
  },

  fr: {
    // Common
    back: 'Retour',
    continue: 'Continuer',
    cancel: 'Annuler',
    ok: 'OK',
    error: 'Erreur',
    success: 'Succès',
    loading: 'Chargement...',
    refresh: 'Actualiser',
    search: 'Rechercher',
    retry: 'Réessayer',
    openSettings: 'Ouvrir les paramètres',

    // Location Errors
    locationError: 'Erreur de localisation',
    locationServicesDisabled: 'Services de localisation désactivés',
    locationPermissionDenied: 'Permission de localisation refusée',
    enableLocationServices: 'Veuillez activer les services de localisation dans les paramètres de votre appareil.',
    grantLocationPermission: 'Veuillez accorder l\'accès à la localisation dans les paramètres de votre appareil.',

    // Home Screen
    appTitle: 'MABUS',
    appSubtitle: 'Système de suivi intelligent des bus du Maroc',
    imADriver: 'Je suis chauffeur',
    imAPassenger: 'Je suis passager',
    driverDescription: 'Partagez votre position avec les passagers',
    passengerDescription: 'Suivez les bus en temps réel',
    selectLine: 'Sélectionnez votre ligne',
    trackRoute: 'Suivez votre itinéraire',
    manageShifts: 'Gérer les équipes',
    findNearbyBuses: 'Trouver les bus à proximité',
    realTimeTracking: 'Suivi en temps réel',
    etaEstimates: 'Estimations d\'arrivée',

    // Driver Registration
    driverRegistration: 'Inscription Chauffeur',
    createDriverAccount: 'Créez votre compte chauffeur pour commencer à travailler',
    fullName: 'Nom complet',
    email: 'Email',
    phoneNumber: 'Numéro de téléphone',
    busCompany: 'Compagnie de bus',
    city: 'Ville',
    driverLicenseNumber: 'Numéro de permis de conduire',
    busNumber: 'Numéro de bus (Optionnel)',
    password: 'Mot de passe',
    confirmPassword: 'Confirmer le mot de passe',
    createAccount: 'Créer un compte',
    creatingAccount: 'Création du compte...',
    registrationSuccessful: 'Inscription réussie',
    registrationSuccessMessage: 'Votre compte chauffeur a été créé avec succès.',

    // OTP Verification
    otpVerification: 'Vérification OTP',
    enterOtpCode: 'Entrez le code de vérification envoyé à votre téléphone',
    verifyCode: 'Vérifier le code',
    verifying: 'Vérification...',
    resendCode: 'Renvoyer le code',

    // Driver Login
    driverLogin: 'Connexion Chauffeur',
    welcomeBack: 'Bon retour !',
    loginToAccount: 'Connectez-vous à votre compte chauffeur',
    login: 'Se connecter',
    loggingIn: 'Connexion...',
    forgotPassword: 'Mot de passe oublié ?',
    dontHaveAccount: 'Vous n\'avez pas de compte ?',
    registerHere: 'Inscrivez-vous ici',

    // Active Shift
    activeShift: 'Service actif',
    shiftDuration: 'Durée du service',
    currentLine: 'Ligne actuelle',
    endShift: 'Terminer le service',
    pauseTracking: 'Suspendre le suivi',
    resumeTracking: 'Reprendre le suivi',
    shiftEnded: 'Service terminé',
    shiftEndedMessage: 'Votre service a été terminé avec succès.',
    confirmEndShift: 'Êtes-vous sûr de vouloir terminer votre service ?',

    // Driver Login
    driverLogin: 'Connexion Chauffeur',
    welcomeBack: 'Bon retour !',
    loginToAccount: 'Connectez-vous à votre compte chauffeur',
    login: 'Se connecter',
    loggingIn: 'Connexion...',
    forgotPassword: 'Mot de passe oublié ?',
    dontHaveAccount: 'Vous n\'avez pas de compte ?',
    registerHere: 'Inscrivez-vous ici',

    // Line Selection
    selectBusLine: 'Sélectionner la ligne de bus',
    chooseLineForShift: 'Choisissez votre ligne de bus pour ce service',
    startShift: 'Commencer le service',
    startingShift: 'Démarrage du service...',
    enterLineNumber: 'Entrez le numéro de ligne',
    trackBus: 'Suivre le bus',

    // Direction Selection
    selectDirection: 'Sélectionner la direction',
    chooseDirection: 'Choisir la direction',
    continueToDirection: 'Continuer vers la direction',
    directionA: 'Direction A',
    directionB: 'Direction B',

    // Station Information
    routeStations: 'Stations de l\'itinéraire',
    passedStations: 'Stations passées',
    currentLocation: 'Position actuelle',
    upcomingStations: 'Prochaines stations',
    enRoute: 'En route',

    // Active Shift
    activeShift: 'Service actif',
    shiftDuration: 'Durée du service',
    currentLine: 'Ligne actuelle',
    endShift: 'Terminer le service',
    pauseTracking: 'Suspendre le suivi',
    resumeTracking: 'Reprendre le suivi',

    // Passenger Screens
    selectYourCity: 'Sélectionnez votre ville',
    chooseYourCity: 'Choisissez la ville où vous voulez suivre les bus',
    continueToLines: 'Continuer vers les lignes',
    realTimeTracking: 'Suivi en temps réel sur MABUS',

    // Map Screen
    busesInCity: 'bus dans',
    arrivesIn: 'Arrive dans',
    updatingLocations: 'Mise à jour des positions...',

    // Profile
    profile: 'Profil',
    editProfile: 'Modifier le profil',
    saveChanges: 'Enregistrer les modifications',

    // Onboarding
    welcome: 'Bienvenue sur MABUS',
    chooseUserType: 'Système de suivi intelligent des bus du Maroc',
    driverSteps: 'Étapes chauffeur',
    passengerSteps: 'Étapes passager',
    howToUseAsDriver: 'Comment utiliser en tant que chauffeur',
    howToUseAsPassenger: 'Comment utiliser en tant que passager',

    // Validation Messages
    enterFullName: 'Veuillez entrer votre nom complet',
    enterValidEmail: 'Veuillez entrer une adresse email valide',
    passwordTooShort: 'Le mot de passe doit contenir au moins 6 caractères',
    passwordsDoNotMatch: 'Les mots de passe ne correspondent pas',
    enterPhoneNumber: 'Veuillez entrer votre numéro de téléphone',
    selectBusCompany: 'Veuillez sélectionner une compagnie de bus',
    selectCity: 'Veuillez sélectionner votre ville',
    enterLicenseNumber: 'Veuillez entrer votre numéro de permis',
    noCitySelected: 'Aucune ville sélectionnée',
  },

  ar: {
    // Common
    back: 'رجوع',
    continue: 'متابعة',
    cancel: 'إلغاء',
    ok: 'موافق',
    error: 'خطأ',
    success: 'نجح',
    loading: 'جاري التحميل...',
    refresh: 'تحديث',
    search: 'بحث',
    retry: 'إعادة المحاولة',
    openSettings: 'فتح الإعدادات',

    // Location Errors
    locationError: 'خطأ في الموقع',
    locationServicesDisabled: 'خدمات الموقع معطلة',
    locationPermissionDenied: 'تم رفض إذن الموقع',
    enableLocationServices: 'يرجى تفعيل خدمات الموقع في إعدادات جهازك.',
    grantLocationPermission: 'يرجى منح إذن الوصول للموقع في إعدادات جهازك.',
    
    // Home Screen
    appTitle: 'تتبع الحافلات المغرب',
    appSubtitle: 'تتبع الحافلات في الوقت الفعلي للنقل العام في المغرب',
    imADriver: 'أنا سائق',
    imAPassenger: 'أنا راكب',
    driverDescription: 'ابدأ نوبتك وابدأ في تتبع خط الحافلة الخاص بك',
    passengerDescription: 'تتبع الحافلات في الوقت الفعلي وخطط لرحلتك',
    selectLine: 'اختر خطك',
    trackRoute: 'تتبع طريقك',
    manageShifts: 'إدارة النوبات',
    findNearbyBuses: 'العثور على الحافلات القريبة',
    realTimeTracking: 'التتبع في الوقت الفعلي',
    etaEstimates: 'تقديرات وقت الوصول',
    
    // Driver Registration
    driverRegistration: 'تسجيل السائق',
    createDriverAccount: 'أنشئ حساب السائق الخاص بك لبدء العمل',
    fullName: 'الاسم الكامل',
    email: 'البريد الإلكتروني',
    phoneNumber: 'رقم الهاتف',
    busCompany: 'شركة الحافلات',
    city: 'المدينة',
    driverLicenseNumber: 'رقم رخصة السائق',
    busNumber: 'رقم الحافلة (اختياري)',
    password: 'كلمة المرور',
    confirmPassword: 'تأكيد كلمة المرور',
    createAccount: 'إنشاء حساب',
    creatingAccount: 'جاري إنشاء الحساب...',
    registrationSuccessful: 'تم التسجيل بنجاح',
    registrationSuccessMessage: 'تم إنشاء حساب السائق الخاص بك بنجاح. يمكنك الآن تسجيل الدخول.',
    
    // OTP Verification
    otpVerification: 'التحقق من الرمز',
    otpSent: 'لقد أرسلنا رمز التحقق إلى رقم هاتفك',
    enterOtp: 'أدخل رمز التحقق',
    verifyCode: 'تحقق من الرمز',
    resendCode: 'إعادة إرسال الرمز',

    // Driver Login
    driverLogin: 'تسجيل دخول السائق',
    welcomeBack: 'مرحباً بعودتك!',
    loginToAccount: 'تسجيل الدخول إلى حساب السائق',
    login: 'تسجيل الدخول',
    loggingIn: 'جاري تسجيل الدخول...',
    forgotPassword: 'نسيت كلمة المرور؟',
    dontHaveAccount: 'ليس لديك حساب؟',
    registerHere: 'سجل هنا',

    // Active Shift
    activeShift: 'النوبة النشطة',
    shiftDuration: 'مدة النوبة',
    currentLine: 'الخط الحالي',
    endShift: 'إنهاء النوبة',
    pauseTracking: 'إيقاف التتبع مؤقتاً',
    resumeTracking: 'استئناف التتبع',
    shiftEnded: 'انتهت النوبة',
    shiftEndedMessage: 'تم إنهاء نوبتك بنجاح.',
    confirmEndShift: 'هل أنت متأكد من أنك تريد إنهاء نوبتك؟',
    
    // City Selection
    selectYourCity: 'اختر مدينتك',
    chooseYourCity: 'اختر مدينتك',
    selectCityDescription: 'اختر المدينة التي تريد تتبع الحافلات فيها',
    searchCities: 'البحث في المدن...',
    continueToLines: 'متابعة إلى خطوط الحافلات',
    noResultsFound: 'لم يتم العثور على مدن تطابق',
    tryDifferentSearch: 'جرب البحث بمصطلح مختلف',
    
    // Line Selection
    selectBusLine: 'اختر خط الحافلة',
    searchLines: 'البحث في خطوط الحافلات...',
    availableLines: 'الخطوط المتاحة',
    selectedBy: 'مختار من قبل',
    startShift: 'بدء النوبة',
    chooseLineForShift: 'اختر خط الحافلة لهذه النوبة',
    startingShift: 'بدء النوبة...',
    enterLineNumber: 'أدخل رقم الخط',
    trackBus: 'تتبع الحافلة',

    // Direction Selection
    selectDirection: 'اختر الاتجاه',
    chooseDirection: 'اختر الاتجاه',
    continueToDirection: 'متابعة إلى الاتجاه',
    directionA: 'الاتجاه أ',
    directionB: 'الاتجاه ب',

    // Station Information
    routeStations: 'محطات الطريق',
    passedStations: 'المحطات المارة',
    currentLocation: 'الموقع الحالي',
    upcomingStations: 'المحطات القادمة',
    enRoute: 'في الطريق',
    
    // Tracking Map
    interactiveMap: 'خريطة تفاعلية',
    mapNotSupported: 'الخرائط مدعومة بالكامل في هذا التطبيق',
    tapToToggle: 'اضغط لإخفاء/إظهار معلومات خط الحافلة',
    youAreHere: 'أنت هنا',
    locationAccessDenied: 'تم رفض الوصول للموقع',
    enable: 'تمكين',
    lineVarieties: 'أنواع الخط',
    busesInCity: 'حافلات في',
    nextStop: 'التالي',
    eta: 'وقت الوصول المتوقع',
    details: 'التفاصيل',
    arrivesIn: 'يصل في',
    occupancy: 'الإشغال',
    updatingLocations: 'تحديث المواقع...',
    
    // Profile
    profile: 'الملف الشخصي',
    editProfile: 'تعديل الملف الشخصي',
    driverInfo: 'معلومات السائق',
    
    // Onboarding
    welcome: 'مرحباً بك في تتبع الحافلات المغرب',
    chooseUserType: 'اختر نوع المستخدم للبدء',
    driverSteps: 'خطوات السائق',
    passengerSteps: 'خطوات الراكب',
    howToUseAsDriver: 'كيفية الاستخدام كسائق',
    howToUseAsPassenger: 'كيفية الاستخدام كراكب',
    
    // Validation Messages
    enterFullName: 'يرجى إدخال اسمك الكامل',
    enterValidEmail: 'يرجى إدخال عنوان بريد إلكتروني صالح',
    passwordTooShort: 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',
    passwordsDoNotMatch: 'كلمات المرور غير متطابقة',
    enterPhoneNumber: 'يرجى إدخال رقم هاتفك',
    selectBusCompany: 'يرجى اختيار شركة الحافلات',
    selectCity: 'يرجى اختيار مدينتك',
    enterLicenseNumber: 'يرجى إدخال رقم رخصتك',
    noCitySelected: 'لم يتم اختيار مدينة',
  }
});

// Set locale based on device language with fallback
const deviceLanguage = Localization.getLocales()[0]?.languageCode ?? 'en';
const supportedLanguages = ['en', 'fr', 'ar'];

// Map device language to supported language
let selectedLanguage = 'en'; // default fallback
if (supportedLanguages.includes(deviceLanguage)) {
  selectedLanguage = deviceLanguage;
} else if (deviceLanguage.startsWith('fr')) {
  selectedLanguage = 'fr';
} else if (deviceLanguage.startsWith('ar')) {
  selectedLanguage = 'ar';
}

i18n.locale = selectedLanguage;
i18n.enableFallback = true;

export default i18n;
