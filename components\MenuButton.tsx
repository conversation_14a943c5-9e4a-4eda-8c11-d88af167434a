import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, shadowStyles } from '@/styles/commonStyles';

interface MenuButtonProps {
  onPress: () => void;
  style?: any;
  iconColor?: string;
  backgroundColor?: string;
  position?: 'left' | 'right';
}

export default function MenuButton({
  onPress,
  style,
  iconColor = colors.card,
  backgroundColor = colors.primary,
  position = 'left'
}: MenuButtonProps) {
  const positionStyle = position === 'right' ? styles.menuButtonRight : styles.menuButtonLeft;

  return (
    <TouchableOpacity
      style={[styles.menuButton, positionStyle, { backgroundColor }, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <IconSymbol name="line.3.horizontal" size={20} color={iconColor} />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  menuButton: {
    position: 'absolute',
    top: 50,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
    ...shadowStyles.medium,
  },
  menuButtonLeft: {
    left: 20,
  },
  menuButtonRight: {
    right: 20,
  },
});
