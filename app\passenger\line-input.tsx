
import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { IconSymbol } from '@/components/IconSymbol';
import { colors, commonStyles, buttonStyles } from '@/styles/commonStyles';

import LoadingSpinner from '@/components/LoadingSpinner';
import i18n from '@/localization/i18n';

export default function LineInput() {
  const router = useRouter();
  const { city, cityName } = useLocalSearchParams();
  const [lineNumber, setLineNumber] = useState('');

  // Mock data for popular lines in the selected city
  const popularLines = [
    { id: 'L22', name: 'L22', description: 'City Center ↔ Airport' },
    { id: 'L15', name: 'L15', description: 'University ↔ Beach' },
    { id: 'L30', name: 'L30', description: 'Train Station ↔ Mall' },
    { id: 'L45', name: 'L45', description: 'Hospital ↔ Stadium' },
    { id: 'L12', name: 'L12', description: 'Port ↔ Industrial Zone' },
  ];

  const handleLineSelect = (line: string) => {
    console.log('Selected line:', line);
    setLineNumber(line);
  };

  const handleTrackBus = () => {
    if (!lineNumber) {
      Alert.alert('Error', 'Please enter or select a bus line number');
      return;
    }

    console.log('Navigating to direction selection for line:', lineNumber);
    router.push(`/passenger/line-direction?city=${city}&cityName=${cityName}&lineNumber=${lineNumber}`);
  };

  const handleBack = () => {
    console.log('Going back to city selection');
    router.back();
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: i18n.t('selectBusLine'),
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.card,
          headerLeft: () => (
            <TouchableOpacity onPress={handleBack} style={styles.backButton}>
              <IconSymbol name="chevron.left" size={24} color={colors.card} />
            </TouchableOpacity>
          ),
        }}
      />
      <View style={[commonStyles.container, { backgroundColor: colors.background }]}>


        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={[commonStyles.title, { color: colors.text }]}>
              {i18n.t('selectBusLine')}
            </Text>
            <Text style={[commonStyles.textSecondary, { textAlign: 'center' }]}>
              in {cityName}
            </Text>
          </View>

          <View style={styles.inputSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {i18n.t('enterLineNumber')}
            </Text>
            <TextInput
              style={[commonStyles.input, { backgroundColor: colors.card }]}
              placeholder="e.g., L22, L15, L30..."
              placeholderTextColor={colors.textSecondary}
              value={lineNumber}
              onChangeText={setLineNumber}
              autoCapitalize="characters"
              autoCorrect={false}
            />
          </View>

          <View style={styles.popularSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Popular Lines
            </Text>
            <View style={styles.popularGrid}>
              {popularLines.map((line) => (
                <TouchableOpacity
                  key={line.id}
                  style={[
                    styles.popularCard,
                    {
                      backgroundColor: colors.card,
                      borderColor: lineNumber === line.name ? colors.primary : colors.border,
                      borderWidth: lineNumber === line.name ? 2 : 1,
                    }
                  ]}
                  onPress={() => handleLineSelect(line.name)}
                  activeOpacity={0.7}
                >
                  <Text style={[styles.popularLineName, { color: colors.text }]}>
                    {line.name}
                  </Text>
                  <Text style={[styles.popularLineDesc, { color: colors.textSecondary }]}>
                    {line.description}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.actionContainer}>
            <TouchableOpacity
              style={[
                buttonStyles.passenger,
                {
                  opacity: lineNumber ? 1 : 0.5,
                  backgroundColor: lineNumber ? colors.passenger : colors.textSecondary
                }
              ]}
              onPress={handleTrackBus}
              disabled={!lineNumber}
            >
              <Text style={[commonStyles.buttonText, { color: colors.card }]}>
                {i18n.t('continueToDirection')}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>




      </View>
    </>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  inputSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  popularSection: {
    marginBottom: 30,
  },
  popularGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  popularCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  popularLineName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  popularLineDesc: {
    fontSize: 12,
    lineHeight: 16,
  },
  actionContainer: {
    marginTop: 'auto',
    paddingTop: 20,
  },
});
