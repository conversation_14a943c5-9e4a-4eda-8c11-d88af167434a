
import { StyleSheet, ViewStyle, TextStyle, Platform } from 'react-native';

export const colors = {
  background: '#F8F9FA',      // Light gray background
  text: '#1A1A1A',            // Rich dark gray
  textSecondary: '#6B7280',   // Medium gray
  primary: '#1B5E20',         // MABUS Dark Green (from icon)
  secondary: '#FF6B35',       // MABUS Orange (from icon gradient)
  accent: '#FFB74D',          // MABUS Light Orange (from icon gradient)
  passenger: '#FBAE1F',       // MABUS Passenger Yellow
  driver: '#E53935',          // MABUS Red (from icon)
  card: '#FFFFFF',            // Pure white
  highlight: '#FEF7E0',       // Light passenger yellow tint
  error: '#D32F2F',           // MABUS Red variant
  success: '#388E3C',         // MABUS Green variant
  warning: '#FF9800',         // Orange warning
  border: '#E5E7EB',          // Light border
  gradient: {
    start: '#FFB74D',         // Light orange
    middle: '#FF8A65',        // Medium orange
    end: '#FF6B35',           // Dark orange
  },
  shadow: Platform.select({
    ios: 'rgba(0, 0, 0, 0.1)',
    android: 'rgba(0, 0, 0, 0.15)',
    web: 'rgba(0, 0, 0, 0.1)',
  }),
};

// Font family - using system fonts as fallback since PP Agrandir is not available
export const fontFamily = {
  regular: Platform.select({
    ios: 'System',
    android: 'Roboto',
    web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  }),
  medium: Platform.select({
    ios: 'System',
    android: 'Roboto-Medium',
    web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  }),
  bold: Platform.select({
    ios: 'System',
    android: 'Roboto-Bold',
    web: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  }),
};

// Enhanced shadow styles for cross-platform compatibility
export const shadowStyles = {
  small: Platform.select({
    ios: {
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 1,
      shadowRadius: 4,
    },
    android: {
      elevation: 3,
    },
    web: {
      boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
    },
  }),
  medium: Platform.select({
    ios: {
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 1,
      shadowRadius: 8,
    },
    android: {
      elevation: 6,
    },
    web: {
      boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.12)',
    },
  }),
  large: Platform.select({
    ios: {
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 1,
      shadowRadius: 16,
    },
    android: {
      elevation: 12,
    },
    web: {
      boxShadow: '0px 8px 32px rgba(0, 0, 0, 0.15)',
    },
  }),
};

export const buttonStyles = StyleSheet.create({
  primary: {
    backgroundColor: colors.primary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Platform.OS === 'ios' ? 24 : 28,
    ...shadowStyles.small,
  },
  secondary: {
    backgroundColor: colors.secondary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Platform.OS === 'ios' ? 24 : 28,
    ...shadowStyles.small,
  },
  passenger: {
    backgroundColor: colors.passenger,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Platform.OS === 'ios' ? 24 : 28,
    ...shadowStyles.small,
  },
  driver: {
    backgroundColor: colors.driver,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Platform.OS === 'ios' ? 24 : 28,
    ...shadowStyles.small,
  },
  accent: {
    backgroundColor: colors.accent,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Platform.OS === 'ios' ? 24 : 28,
    ...shadowStyles.small,
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: colors.primary,
    paddingVertical: 14,
    paddingHorizontal: 30,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Platform.OS === 'ios' ? 24 : 28,
  },
  elevated: {
    backgroundColor: colors.primary,
    paddingVertical: 18,
    paddingHorizontal: 36,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Platform.OS === 'ios' ? 28 : 36,
    ...shadowStyles.medium,
  },
});

export const commonStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 44 : 52,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 44 : 52,
  },
  title: {
    fontSize: 32,
    fontWeight: Platform.OS === 'ios' ? '700' : 'bold',
    fontFamily: fontFamily.bold,
    color: colors.text,
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: Platform.OS === 'ios' ? -0.5 : 0,
  },
  subtitle: {
    fontSize: 20,
    fontWeight: Platform.OS === 'ios' ? '600' : '700',
    fontFamily: fontFamily.medium,
    color: colors.text,
    textAlign: 'center',
    marginBottom: 20,
    letterSpacing: Platform.OS === 'ios' ? -0.3 : 0,
  },
  text: {
    fontSize: 16,
    fontFamily: fontFamily.regular,
    color: colors.text,
    lineHeight: 24,
    letterSpacing: Platform.OS === 'ios' ? -0.1 : 0,
  },
  textSecondary: {
    fontSize: 14,
    fontFamily: fontFamily.regular,
    color: colors.textSecondary,
    lineHeight: 20,
    letterSpacing: Platform.OS === 'ios' ? -0.1 : 0,
  },
  card: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 20,
    marginVertical: 10,
    ...shadowStyles.small,
  },
  cardElevated: {
    backgroundColor: colors.card,
    borderRadius: 20,
    padding: 24,
    marginVertical: 12,
    marginBottom: Platform.OS === 'ios' ? 24 : 28,
    ...shadowStyles.medium,
  },
  input: {
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    fontSize: 16,
    fontFamily: fontFamily.regular,
    color: colors.text,
    marginVertical: 10,
    ...shadowStyles.small,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    fontFamily: fontFamily.medium,
    color: colors.card,
    letterSpacing: Platform.OS === 'ios' ? -0.1 : 0.2,
  },
  buttonTextOutline: {
    fontSize: 16,
    fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
    fontFamily: fontFamily.medium,
    color: colors.primary,
    letterSpacing: Platform.OS === 'ios' ? -0.1 : 0.2,
  },
  section: {
    marginVertical: 20,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  spacer: {
    height: 24,
  },
  spacerLarge: {
    height: 40,
  },
  fadeIn: {
    opacity: 1,
  },
  fadeOut: {
    opacity: 0,
  },
  screenContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  bottomSafeArea: {
    paddingBottom: Platform.select({
      ios: 34,
      android: 24,
      web: 20,
    }),
  },
});

// Animation configurations for consistent transitions
export const animationConfig = {
  spring: {
    damping: 20,
    stiffness: 120,
    mass: 1,
  },
  timing: {
    duration: 300,
  },
  springFast: {
    damping: 25,
    stiffness: 200,
    mass: 0.8,
  },
  timingFast: {
    duration: 200,
  },
};
