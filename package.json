{"name": "Natively", "version": "1.0.0", "main": "index.ts", "scripts": {"dev": "expo start --tunnel", "dev:clear": "expo start --tunnel --clear", "dev:reset": "expo start --tunnel --clear --reset-cache", "start": "expo start --tunnel", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:web": "expo export -p web", "build:android": "expo prebuild -p android", "lint": "eslint ."}, "dependencies": {"@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/runtime": "^7.26.9", "@bacons/apple-targets": "^3.0.2", "@expo/metro-runtime": "~6.1.1", "@expo/ngrok": "^4.1.3", "@expo/vector-icons": "^15.0.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "8.4.4", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@types/difflib": "^0.2.7", "difflib": "^0.2.4", "eas": "^0.1.0", "expo": "~54.0.1", "expo-background-fetch": "^14.0.7", "expo-blur": "^15.0.6", "expo-constants": "~18.0.8", "expo-font": "^14.0.7", "expo-glass-effect": "^0.1.1", "expo-haptics": "^15.0.6", "expo-image-picker": "^17.0.7", "expo-linear-gradient": "^15.0.6", "expo-linking": "^8.0.7", "expo-localization": "~17.0.7", "expo-location": "^19.0.7", "expo-maps": "~0.12.8", "expo-network": "^8.0.7", "expo-notifications": "^0.32.12", "expo-router": "^6.0.0", "expo-splash-screen": "^31.0.8", "expo-status-bar": "~3.0.7", "expo-symbols": "^1.0.6", "expo-system-ui": "^6.0.7", "expo-task-manager": "^14.0.7", "expo-web-browser": "^15.0.6", "i18n-js": "^4.5.1", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-css-interop": "^0.1.22", "react-native-edge-to-edge": "^1.7.0", "react-native-gesture-handler": "^2.28.0", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.16.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.21.1", "react-native-webview": "13.15.0", "react-native-worklets": "0.5.1", "react-router-dom": "^7.1.3", "workbox-precaching": "^7.3.0", "workbox-webpack-plugin": "^7.3.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@eslint/js": "^9.19.0", "@types/react": "~19.1.12", "@typescript-eslint/eslint-plugin": "^8.45.0", "@typescript-eslint/parser": "^8.45.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.37.0", "eslint-config-expo": "~10.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.4", "globals": "^15.14.0", "typescript": "^5.8.3", "webpack-cli": "^6.0.1"}, "resolutions": {"@expo/prebuild-config": "latest"}, "private": true}