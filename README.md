# MABUS: Moroccan Bus Tracking System 🚌

MABUS is a comprehensive mobile application for tracking public buses in Morocco in real-time. The app provides a clean, modern, and interactive user experience with dual interfaces for both passengers and drivers.

## Features

### For Passengers 👥

- **Multi-language Support**: Available in English, French (Français), and Arabic (العربية)
- **City Selection**: Browse through available cities in Morocco
- **Line Selection**: View all bus lines available in a selected city
- **Real-time Tracking**: See buses moving on the map in real-time with live location updates
- **Station Information**: View passed and upcoming stations with visual differentiation
- **Interactive UI**: Modern design with smooth animations and haptic feedback

### For Drivers 🚗

- **Driver Authentication**: Secure login system with persistent sessions
- **Shift Management**: Start, pause, and end shifts with duration tracking
- **Line Assignment**: Select and manage assigned bus lines
- **Location Sharing**: Share real-time location with passengers
- **Profile Management**: Update driver information and preferences

## Tech Stack

### Frontend (Mobile App)

- **Framework**: React Native with Expo SDK 54
- **Navigation**: Expo Router (file-based routing)
- **Maps**: React Native Maps with custom markers
- **Location**: Expo Location with background tracking
- **Storage**: AsyncStorage for persistent data
- **Animations**: React Native Reanimated 3
- **Gestures**: React Native Gesture Handler
- **Internationalization**: Custom i18n system with 3 languages
- **Real-time Updates**: Socket.IO Client

### Backend

- **Server**: Node.js with Express
- **Database**: PostgreSQL
- **Real-time Communication**: Socket.IO

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- PostgreSQL database
- Expo CLI

### Installation

1. Clone the repository

```bash
git clone https://github.com/yourusername/MABUS.git
cd MABUS
```

2. Install dependencies for the mobile app

```bash
npm install
```

3. Install dependencies for the backend

```bash
cd backend
npm install
```

4. Set up the database

```bash
# Create a PostgreSQL database named 'mabus'
# Then run the schema.sql file
psql -U postgres -d mabus -f src/db/schema.sql
```

5. Configure environment variables

```bash
# In the backend directory
cp .env.example .env
# Edit .env with your database credentials
```

### Running the App

1. Start the backend server

```bash
cd backend
npm run dev
```

2. Start the mobile app

```bash
# In the root directory
npm start
```

3. Open the app in your preferred environment (iOS simulator, Android emulator, or Expo Go on your physical device)

## Project Structure

```
mabus/
├── app/                  # Mobile app screens (Expo Router)
│   ├── (tabs)/          # Tab-based navigation
│   │   └── (home)/      # Home screen
│   ├── driver/          # Driver-specific screens
│   │   ├── login.tsx    # Driver login
│   │   ├── profile.tsx  # Driver profile
│   │   ├── line-selection.tsx
│   │   └── active-shift.tsx
│   └── passenger/       # Passenger-specific screens
│       ├── city-selection.tsx
│       ├── line-input.tsx
│       ├── line-direction.tsx
│       └── tracking-map.tsx
├── assets/              # Images, icons, and other assets
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components
│   ├── LoadingSpinner.tsx
│   └── SideMenu.tsx
├── constants/           # App constants and configuration
├── hooks/              # Custom React hooks
├── localization/       # Internationalization
│   └── i18n.ts        # Translation files (EN, FR, AR)
├── services/           # API and Socket.IO services
├── styles/             # Common styles and themes
│   └── commonStyles.ts
└── utils/              # Utility functions
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Key Features Implementation

### Multi-language Support

- **Languages**: English, French (Français), Arabic (العربية)
- **Dynamic Switching**: Real-time language switching with immediate UI updates
- **RTL Support**: Proper right-to-left layout support for Arabic

### Driver Authentication

- **Persistent Sessions**: Login state maintained across app restarts
- **Secure Navigation**: Prevents unauthorized access to driver features
- **Profile Management**: Complete driver profile with editable information

### Real-time Tracking

- **Live Location**: GPS-based real-time location sharing
- **Background Tracking**: Continues tracking when app is minimized
- **Station Updates**: Dynamic station information with passed/upcoming indicators

### Modern UI/UX

- **Smooth Animations**: React Native Reanimated 3 for fluid transitions
- **Gesture Support**: Pan gestures and touch interactions
- **Responsive Design**: Adapts to different screen sizes and orientations
- **Theme Support**: Consistent color scheme throughout the app

## Acknowledgements

- Inspired by modern transportation apps
- Built with Expo and React Native
- Uses MABUS branding colors: Orange (#FBAE1F), Green (#1B5E20), Red (#E53935)
